#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for few-shot learning implementation

This script tests the new few-shot learning implementation to ensure
it works correctly with training examples.
"""

import os
from gpt4_evaluator import GPT4Evaluator

# API配置
API_KEY = "sk-1PYK9cXL2KoDuuOKZ3PzeHf9HRxXmsrF6tLnetB4dej8bwoJ"

def test_few_shot_learning():
    """Test the few-shot learning implementation"""
    print("="*60)
    print("TESTING FEW-SHOT LEARNING IMPLEMENTATION")
    print("="*60)
    
    # Load training data
    print("1. Loading training data...")
    train_data_path = "data/evaluation/evaluation/train-evaluation.txt"
    
    if not os.path.exists(train_data_path):
        print(f"❌ Training data file not found: {train_data_path}")
        return False
    
    # Create temporary evaluator to load training data
    temp_evaluator = GPT4Evaluator(api_key="temp")
    train_examples = temp_evaluator.load_evaluation_data(train_data_path)
    print(f"✓ Loaded {len(train_examples)} training examples")
    
    if not train_examples:
        print("❌ No training examples loaded")
        return False
    
    # Show first training example
    print(f"\nFirst training example preview:")
    first_example = train_examples[0]
    print(f"  Will text: {first_example['will_text'][:100]}...")
    print(f"  Entities: {len(first_example['entities'])}")
    print(f"  Relations: {len(first_example['relations'])}")
    
    # Initialize evaluator with few-shot learning
    print(f"\n2. Initializing evaluator with {3}-shot learning...")
    evaluator = GPT4Evaluator(
        api_key=API_KEY,
        train_examples=train_examples,
        num_shots=3
    )
    
    # Test few-shot example selection
    print("\n3. Testing few-shot example selection...")
    selected_examples = evaluator._select_few_shot_examples("in_domain")
    print(f"✓ Selected {len(selected_examples)} examples for in-domain")
    
    # Test prompt formatting
    print("\n4. Testing prompt formatting...")
    if selected_examples:
        entity_prompt_example = evaluator._format_example_for_entity_prompt(selected_examples[0])
        print(f"✓ Entity prompt example formatted ({len(entity_prompt_example)} chars)")
        print(f"Preview: {entity_prompt_example[:200]}...")
        
        relation_prompt_example = evaluator._format_example_for_relation_prompt(selected_examples[0])
        print(f"✓ Relation prompt example formatted ({len(relation_prompt_example)} chars)")
        print(f"Preview: {relation_prompt_example[:200]}...")
    
    # Test with a simple will text
    print("\n5. Testing entity extraction with few-shot learning...")
    test_will = "I, John Smith, hereby give my house to my son Michael."
    
    try:
        entities = evaluator.extract_entities_from_text(test_will, target_domain="in_domain")
        print(f"✓ Entity extraction successful!")
        print(f"Found {len(entities)} entities:")
        for i, entity in enumerate(entities):
            print(f"  {i+1}. '{entity.text}' ({entity.label}) [{entity.start}:{entity.end}]")
        
        # Test relation extraction
        print("\n6. Testing relation extraction with few-shot learning...")
        relations = evaluator.extract_relations_from_text(test_will, entities, target_domain="in_domain")
        print(f"✓ Relation extraction successful!")
        print(f"Found {len(relations)} relations:")
        for i, relation in enumerate(relations):
            print(f"  {i+1}. {relation.from_entity} -> {relation.to_entity} ({relation.relation_type})")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during extraction: {e}")
        return False

def test_domain_differences():
    """Test the difference between in-domain and out-of-domain example selection"""
    print("\n" + "="*60)
    print("TESTING DOMAIN-SPECIFIC EXAMPLE SELECTION")
    print("="*60)
    
    # Load training data
    temp_evaluator = GPT4Evaluator(api_key="temp")
    train_examples = temp_evaluator.load_evaluation_data("data/evaluation/evaluation/train-evaluation.txt")
    
    evaluator = GPT4Evaluator(
        api_key=API_KEY,
        train_examples=train_examples,
        num_shots=3
    )
    
    # Test in-domain selection
    in_domain_examples = evaluator._select_few_shot_examples("in_domain")
    print(f"In-domain examples selected: {len(in_domain_examples)}")
    
    # Test out-of-domain selection
    out_domain_examples = evaluator._select_few_shot_examples("out_domain")
    print(f"Out-of-domain examples selected: {len(out_domain_examples)}")
    
    # Note: In current implementation, both return the same examples
    # In a more sophisticated version, you would filter by state/domain
    print("Note: Current implementation uses random selection.")
    print("For true domain separation, you would need state/domain metadata.")
    
    return True

def main():
    """Run all tests"""
    try:
        success1 = test_few_shot_learning()
        success2 = test_domain_differences()
        
        print("\n" + "="*60)
        if success1 and success2:
            print("🎉 ALL TESTS PASSED!")
            print("Few-shot learning implementation is working correctly.")
            print("\nYou can now run the improved evaluation with:")
            print("python src/run_evaluation.py --max-examples 3 --num-shots 3")
        else:
            print("❌ SOME TESTS FAILED")
            print("Please check the error messages above.")
        print("="*60)
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
