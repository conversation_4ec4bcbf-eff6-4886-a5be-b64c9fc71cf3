#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Demo script for GPT-4 evaluation framework

This script demonstrates the evaluation process using mock predictions
instead of actual GPT-4 API calls. Useful for testing and demonstration
when API key is not available.
"""

import random
import json
import os
from datetime import datetime
from gpt4_evaluator import GPT4Evaluator, Entity, Relation, EvaluationResult

class MockGPT4Evaluator(GPT4Evaluator):
    """Mock evaluator that generates random predictions instead of calling GPT-4"""
    
    def __init__(self):
        # Initialize without API key
        self.entity_prompt_template = self._load_prompt_template("prompt/evaluation_prompt_for_entity.txt")
        self.relation_prompt_template = self._load_prompt_template("prompt/evaluation_prompt_for_relation.txt")
        
    def _call_gpt4(self, prompt: str, max_retries: int = 3) -> str:
        """Mock GPT-4 call - not used in this demo"""
        return "Mock response"
    
    def extract_entities_from_text(self, will_text: str) -> list:
        """Generate mock entity predictions based on simple heuristics"""
        entities = []
        
        # Simple heuristics to find potential entities
        words = will_text.split()
        
        for i, word in enumerate(words):
            start_pos = will_text.find(word, sum(len(w) + 1 for w in words[:i]))
            end_pos = start_pos + len(word)
            
            # Mock entity detection with some randomness
            if word.lower() in ['i', 'me', 'my', 'myself']:
                if random.random() > 0.3:  # 70% chance to detect
                    entities.append(Entity(
                        text=word,
                        start=start_pos,
                        end=end_pos,
                        label="TESTATOR"
                    ))
            elif '[person-' in word.lower() or word.lower() in ['spouse', 'husband', 'wife', 'son', 'daughter', 'children']:
                if random.random() > 0.4:  # 60% chance to detect
                    entities.append(Entity(
                        text=word,
                        start=start_pos,
                        end=end_pos,
                        label="BENEFICIARY"
                    ))
            elif word.lower() in ['will', 'testament']:
                if random.random() > 0.2:  # 80% chance to detect
                    entities.append(Entity(
                        text=word,
                        start=start_pos,
                        end=end_pos,
                        label="WILL"
                    ))
            elif word.lower() in ['property', 'estate', 'house', 'money', 'assets']:
                if random.random() > 0.5:  # 50% chance to detect
                    entities.append(Entity(
                        text=word,
                        start=start_pos,
                        end=end_pos,
                        label="ASSET"
                    ))
        
        # Add some random false positives
        if random.random() > 0.7:
            entities.append(Entity(
                text="random_entity",
                start=0,
                end=13,
                label=random.choice(["TESTATOR", "BENEFICIARY", "ASSET", "WILL"])
            ))
        
        return entities
    
    def extract_relations_from_text(self, will_text: str, entities: list) -> list:
        """Generate mock relation predictions"""
        relations = []
        
        # Simple heuristic: create relations between different entity types
        testators = [e for e in entities if e.label == "TESTATOR"]
        beneficiaries = [e for e in entities if e.label == "BENEFICIARY"]
        assets = [e for e in entities if e.label == "ASSET"]
        wills = [e for e in entities if e.label == "WILL"]
        
        # TESTATOR-BENEFICIARY relations
        for testator in testators:
            for beneficiary in beneficiaries:
                if random.random() > 0.6:  # 40% chance
                    relations.append(Relation(
                        from_entity=f"entity_{entities.index(testator)}",
                        to_entity=f"entity_{entities.index(beneficiary)}",
                        relation_type="TESTATOR-BENEFICIARY"
                    ))
        
        # TESTATOR-ASSET relations
        for testator in testators:
            for asset in assets:
                if random.random() > 0.7:  # 30% chance
                    relations.append(Relation(
                        from_entity=f"entity_{entities.index(testator)}",
                        to_entity=f"entity_{entities.index(asset)}",
                        relation_type="TESTATOR-ASSET"
                    ))
        
        # BENEFICIARY-ASSET relations
        for beneficiary in beneficiaries:
            for asset in assets:
                if random.random() > 0.8:  # 20% chance
                    relations.append(Relation(
                        from_entity=f"entity_{entities.index(beneficiary)}",
                        to_entity=f"entity_{entities.index(asset)}",
                        relation_type="BENEFICIARY-ASSET"
                    ))
        
        # TESTATOR-WILL relations
        for testator in testators:
            for will in wills:
                if random.random() > 0.5:  # 50% chance
                    relations.append(Relation(
                        from_entity=f"entity_{entities.index(testator)}",
                        to_entity=f"entity_{entities.index(will)}",
                        relation_type="TESTATOR-WILL"
                    ))
        
        return relations

def run_demo_evaluation():
    """Run a demonstration evaluation with mock predictions"""
    print("="*60)
    print("GPT-4 LEGAL WILLS IE EVALUATION DEMO")
    print("="*60)
    print("This demo uses mock predictions to demonstrate the evaluation framework.")
    print("For actual GPT-4 evaluation, use run_evaluation.py with an API key.\n")
    
    # Initialize mock evaluator
    evaluator = MockGPT4Evaluator()
    
    # Load a small sample of evaluation data
    dataset_path = "data/evaluation/evaluation/dev-evaluation.txt"
    
    if not os.path.exists(dataset_path):
        print(f"Error: Dataset file {dataset_path} not found.")
        print("Please ensure you have run the data preparation steps.")
        return
    
    print(f"Loading evaluation data from: {dataset_path}")
    examples = evaluator.load_evaluation_data(dataset_path)
    
    # Limit to first 5 examples for demo
    examples = examples[:5]
    print(f"Using {len(examples)} examples for demonstration\n")
    
    # Track predictions and gold standards
    all_predicted_entities = []
    all_gold_entities = []
    all_predicted_relations = []
    all_gold_relations = []
    
    # Process each example
    for i, example in enumerate(examples):
        print(f"Processing example {i+1}/{len(examples)}...")
        print(f"  Will text: {example['will_text'][:100]}...")
        
        # Generate mock predictions
        predicted_entities = evaluator.extract_entities_from_text(example['will_text'])
        predicted_relations = evaluator.extract_relations_from_text(example['will_text'], predicted_entities)
        
        print(f"  Predicted {len(predicted_entities)} entities, {len(predicted_relations)} relations")
        print(f"  Gold standard: {len(example['entities'])} entities, {len(example['relations'])} relations")
        
        # Collect predictions and gold standards
        all_predicted_entities.extend(predicted_entities)
        all_gold_entities.extend(example['entities'])
        all_predicted_relations.extend(predicted_relations)
        all_gold_relations.extend(example['relations'])
        
        print("  ✓ Processed\n")
    
    # Calculate evaluation metrics
    print("Calculating evaluation metrics...")
    
    # Entity evaluation
    entity_result = evaluator.evaluate_entities(all_predicted_entities, all_gold_entities)
    
    # Relation evaluation
    relation_result = evaluator.evaluate_relations(
        all_predicted_relations, all_gold_relations,
        all_predicted_entities, all_gold_entities
    )
    
    # Print results
    print("\n" + "="*50)
    print("ENTITY EXTRACTION RESULTS")
    print("="*50)
    print(f"Precision: {entity_result.precision:.4f}")
    print(f"Recall:    {entity_result.recall:.4f}")
    print(f"F1 Score:  {entity_result.f1:.4f}")
    print(f"True Positives:  {entity_result.true_positives}")
    print(f"False Positives: {entity_result.false_positives}")
    print(f"False Negatives: {entity_result.false_negatives}")
    
    print("\n" + "="*50)
    print("RELATION EXTRACTION RESULTS")
    print("="*50)
    print(f"Precision: {relation_result.precision:.4f}")
    print(f"Recall:    {relation_result.recall:.4f}")
    print(f"F1 Score:  {relation_result.f1:.4f}")
    print(f"True Positives:  {relation_result.true_positives}")
    print(f"False Positives: {relation_result.false_positives}")
    print(f"False Negatives: {relation_result.false_negatives}")
    
    # Save demo results
    demo_results = {
        "demo_info": {
            "timestamp": datetime.now().isoformat(),
            "examples_processed": len(examples),
            "note": "These are mock predictions for demonstration purposes"
        },
        "entity_extraction": {
            "precision": entity_result.precision,
            "recall": entity_result.recall,
            "f1": entity_result.f1,
            "true_positives": entity_result.true_positives,
            "false_positives": entity_result.false_positives,
            "false_negatives": entity_result.false_negatives
        },
        "relation_extraction": {
            "precision": relation_result.precision,
            "recall": relation_result.recall,
            "f1": relation_result.f1,
            "true_positives": relation_result.true_positives,
            "false_positives": relation_result.false_positives,
            "false_negatives": relation_result.false_negatives
        }
    }
    
    # Create results directory
    os.makedirs("results", exist_ok=True)
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"results/demo_results_{timestamp}.json"
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(demo_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📊 Demo results saved to: {results_file}")
    
    print("\n" + "="*60)
    print("DEMO COMPLETED!")
    print("="*60)
    print("Note: These results are based on mock predictions and do not")
    print("represent actual GPT-4 performance. For real evaluation, use:")
    print("  python src/run_evaluation.py --api-key YOUR_API_KEY")

if __name__ == "__main__":
    # Set random seed for reproducible demo results
    random.seed(42)
    run_demo_evaluation()
