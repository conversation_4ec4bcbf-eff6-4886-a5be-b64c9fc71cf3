#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for the GPT-4 evaluator

This script tests the evaluation framework without making actual API calls.
It uses mock data to verify that the evaluation logic works correctly.
"""

import sys
import os
from gpt4_evaluator import GPT4Evaluator, Entity, Relation, EvaluationResult

def test_entity_evaluation():
    """Test entity evaluation logic with mock data"""
    print("Testing entity evaluation...")
    
    # Create a mock evaluator (no API key needed for testing)
    evaluator = GPT4Evaluator(api_key="test_key")
    
    # Mock predicted entities
    predicted_entities = [
        Entity(text="I", start=0, end=1, label="TESTATOR"),
        Entity(text="my spouse", start=10, end=19, label="BENEFICIARY"),
        Entity(text="my house", start=25, end=33, label="ASSET"),
        Entity(text="this will", start=40, end=49, label="WILL"),
        Entity(text="extra entity", start=50, end=62, label="TESTATOR")  # False positive
    ]
    
    # Mock gold entities
    gold_entities = [
        {"text": "I", "start": 0, "end": 1, "label": "TESTATOR", "entity_id": "e1"},
        {"text": "my spouse", "start": 10, "end": 19, "label": "BENEFICIARY", "entity_id": "e2"},
        {"text": "my house", "start": 25, "end": 33, "label": "ASSET", "entity_id": "e3"},
        {"text": "this will", "start": 40, "end": 49, "label": "WILL", "entity_id": "e4"},
        {"text": "missed entity", "start": 70, "end": 83, "label": "ASSET", "entity_id": "e5"}  # False negative
    ]
    
    # Evaluate
    result = evaluator.evaluate_entities(predicted_entities, gold_entities)
    
    # Print results
    print(f"  True Positives: {result.true_positives} (expected: 4)")
    print(f"  False Positives: {result.false_positives} (expected: 1)")
    print(f"  False Negatives: {result.false_negatives} (expected: 1)")
    print(f"  Precision: {result.precision:.3f} (expected: 0.800)")
    print(f"  Recall: {result.recall:.3f} (expected: 0.800)")
    print(f"  F1: {result.f1:.3f} (expected: 0.800)")
    
    # Verify results
    assert result.true_positives == 4, f"Expected 4 TP, got {result.true_positives}"
    assert result.false_positives == 1, f"Expected 1 FP, got {result.false_positives}"
    assert result.false_negatives == 1, f"Expected 1 FN, got {result.false_negatives}"
    assert abs(result.precision - 0.8) < 0.001, f"Expected precision 0.8, got {result.precision}"
    assert abs(result.recall - 0.8) < 0.001, f"Expected recall 0.8, got {result.recall}"
    assert abs(result.f1 - 0.8) < 0.001, f"Expected F1 0.8, got {result.f1}"
    
    print("  ✓ Entity evaluation test passed!")

def test_data_loading():
    """Test loading evaluation data from file"""
    print("\nTesting data loading...")
    
    # Create a mock evaluator
    evaluator = GPT4Evaluator(api_key="test_key")
    
    # Test with a small evaluation file
    test_file = "data/evaluation/evaluation/dev-evaluation.txt"
    
    if os.path.exists(test_file):
        examples = evaluator.load_evaluation_data(test_file)
        print(f"  Loaded {len(examples)} examples from {test_file}")
        
        if examples:
            # Check first example structure
            first_example = examples[0]
            print(f"  First example has {len(first_example['entities'])} entities")
            print(f"  First example has {len(first_example['relations'])} relations")
            print(f"  Will text preview: {first_example['will_text'][:100]}...")
            
            # Show some entities
            if first_example['entities']:
                print(f"  Sample entity: {first_example['entities'][0]}")
            
            # Show some relations
            if first_example['relations']:
                print(f"  Sample relation: {first_example['relations'][0]}")
        
        print("  ✓ Data loading test passed!")
    else:
        print(f"  Warning: Test file {test_file} not found, skipping data loading test")

def test_prompt_loading():
    """Test loading prompt templates"""
    print("\nTesting prompt template loading...")
    
    # Create a mock evaluator
    evaluator = GPT4Evaluator(api_key="test_key")
    
    # Check if prompts were loaded
    if evaluator.entity_prompt_template:
        print(f"  Entity prompt loaded: {len(evaluator.entity_prompt_template)} characters")
        print(f"  Entity prompt preview: {evaluator.entity_prompt_template[:100]}...")
    else:
        print("  Warning: Entity prompt template not loaded")
    
    if evaluator.relation_prompt_template:
        print(f"  Relation prompt loaded: {len(evaluator.relation_prompt_template)} characters")
        print(f"  Relation prompt preview: {evaluator.relation_prompt_template[:100]}...")
    else:
        print("  Warning: Relation prompt template not loaded")
    
    print("  ✓ Prompt loading test completed!")

def test_response_parsing():
    """Test parsing GPT-4 response format"""
    print("\nTesting response parsing...")
    
    # Create a mock evaluator
    evaluator = GPT4Evaluator(api_key="test_key")
    
    # Mock entity response
    entity_response = """
Annotation Text: I
Start Index: 0
End Index: 1
Annotation Labels: ['TESTATOR']
---
Annotation Text: my spouse
Start Index: 10
End Index: 19
Annotation Labels: ['BENEFICIARY']
---
Annotation Text: my house
Start Index: 25
End Index: 33
Annotation Labels: ['ASSET']
---
"""
    
    entities = evaluator._parse_entity_response(entity_response)
    print(f"  Parsed {len(entities)} entities from response")
    
    for i, entity in enumerate(entities):
        print(f"    Entity {i+1}: '{entity.text}' ({entity.label}) [{entity.start}:{entity.end}]")
    
    # Mock relation response
    relation_response = """
Relation Labels: ['TESTATOR-BENEFICIARY']
to_id: entity_1
from_id: entity_0
---
Relation Labels: ['TESTATOR-ASSET']
to_id: entity_2
from_id: entity_0
---
"""
    
    relations = evaluator._parse_relation_response(relation_response)
    print(f"  Parsed {len(relations)} relations from response")
    
    for i, relation in enumerate(relations):
        print(f"    Relation {i+1}: {relation.from_entity} -> {relation.to_entity} ({relation.relation_type})")
    
    print("  ✓ Response parsing test passed!")

def main():
    """Run all tests"""
    print("="*60)
    print("TESTING GPT-4 EVALUATOR FRAMEWORK")
    print("="*60)
    
    try:
        test_entity_evaluation()
        test_data_loading()
        test_prompt_loading()
        test_response_parsing()
        
        print("\n" + "="*60)
        print("ALL TESTS PASSED! ✓")
        print("="*60)
        print("\nThe evaluation framework is ready to use.")
        print("To run actual GPT-4 evaluation, use:")
        print("  python src/run_evaluation.py --api-key YOUR_API_KEY --max-examples 5")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
