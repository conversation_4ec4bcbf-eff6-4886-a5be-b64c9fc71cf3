#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPT-4 Evaluator for Legal Wills Information Extraction

This module provides functionality to evaluate GPT-4's performance on
entity and relation extraction tasks from legal wills.
"""

import json
import os
import time
import re
from typing import List, Dict, Tuple, Optional
import argparse
from dataclasses import dataclass
import requests

@dataclass
class Entity:
    """Represents an extracted entity"""
    text: str
    start: int
    end: int
    label: str
    entity_id: Optional[str] = None

@dataclass
class Relation:
    """Represents an extracted relation"""
    from_entity: str
    to_entity: str
    relation_type: str
    from_id: Optional[str] = None
    to_id: Optional[str] = None

@dataclass
class EvaluationResult:
    """Stores evaluation metrics"""
    precision: float
    recall: float
    f1: float
    true_positives: int
    false_positives: int
    false_negatives: int

class GPT4Evaluator:
    """Main evaluator class for GPT-4 legal wills IE evaluation"""

    def __init__(self, api_key: str, model: str = "gpt-4", api_base: str = None,
                 train_examples: List[Dict] = None, num_shots: int = 3):
        """
        Initialize the evaluator

        Args:
            api_key: API key for the service
            model: GPT model to use (default: gpt-4)
            api_base: Custom API base URL (for third-party services)
            train_examples: Training examples for in-context learning
            num_shots: Number of examples to use for few-shot learning
        """
        self.api_key = api_key
        self.model = model
        self.api_base = api_base or "https://chatapi.littlewheat.com/v1/chat/completions"
        self.train_examples = train_examples or []
        self.num_shots = num_shots

        # Load basic instruction templates (without fixed examples)
        self.entity_instruction = self._load_entity_instruction()
        self.relation_instruction = self._load_relation_instruction()

    def _load_prompt_template(self, filepath: str) -> str:
        """Load prompt template from file"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                return f.read()
        except FileNotFoundError:
            print(f"Warning: Prompt template {filepath} not found")
            return ""

    def _load_entity_instruction(self) -> str:
        """Load entity extraction instruction without fixed examples"""
        return """Your task is to identify all instances (including pronouns) of TESTATOR, BENEFICIARY, WILL, and ASSET from the will statement provided.

Please extract entities in the following format:
---
Annotation Text: [entity text]
Start Index: [start position]
End Index: [end position]
Annotation Labels: ['ENTITY_TYPE']
---

Entity Types:
- TESTATOR: The person making the will (including pronouns like "I", "my", "me")
- BENEFICIARY: People or entities receiving assets (including pronouns referring to beneficiaries)
- ASSET: Property, money, or other items being bequeathed
- WILL: References to the will document itself"""

    def _load_relation_instruction(self) -> str:
        """Load relation extraction instruction without fixed examples"""
        return """Your task is to identify relationships between entities in the will statement.

Please extract relations in the following format:
---
Relation Labels: ['RELATION_TYPE']
from_id: [entity_id_of_source]
to_id: [entity_id_of_target]
---

Relation Types:
- TESTATOR-BENEFICIARY: Testator gives something to beneficiary
- TESTATOR-ASSET: Testator owns or mentions an asset
- BENEFICIARY-ASSET: Beneficiary receives an asset
- TESTATOR-WILL: Testator creates or refers to the will"""

    def _call_gpt4(self, prompt: str, max_retries: int = 3) -> str:
        """
        Call GPT-4 API with retry logic using custom API endpoint

        Args:
            prompt: The prompt to send to GPT-4
            max_retries: Maximum number of retries

        Returns:
            GPT-4 response text
        """
        for attempt in range(max_retries):
            try:
                # Prepare the request payload
                payload = {
                    "model": self.model,
                    "messages": [
                        {"role": "system", "content": "You are an expert in legal document analysis and information extraction."},
                        {"role": "user", "content": prompt}
                    ],
                    "temperature": 0.0,  # For consistent results
                    "max_tokens": 2000,
                    "stream": False
                }

                # Prepare headers
                headers = {
                    'Authorization': f'Bearer {self.api_key}',
                    'Content-Type': 'application/json'
                }

                # Make the API call
                response = requests.post(
                    self.api_base,
                    headers=headers,
                    json=payload,
                    timeout=60  # 60 second timeout
                )

                # Check if request was successful
                response.raise_for_status()

                # Parse the response
                response_data = response.json()

                if 'choices' in response_data and len(response_data['choices']) > 0:
                    return response_data['choices'][0]['message']['content']
                else:
                    raise Exception(f"Unexpected response format: {response_data}")

            except Exception as e:
                print(f"API call attempt {attempt + 1} failed: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff
                else:
                    raise e

    def _select_few_shot_examples(self, target_domain: str = "in_domain") -> List[Dict]:
        """
        Select few-shot examples from training data

        Args:
            target_domain: "in_domain" for same-state examples, "out_domain" for different-state

        Returns:
            List of selected training examples
        """
        if not self.train_examples:
            return []

        # For simplicity, randomly select examples
        # In a more sophisticated implementation, you could select based on similarity
        import random

        available_examples = self.train_examples.copy()
        random.shuffle(available_examples)

        # Select up to num_shots examples
        selected = available_examples[:min(self.num_shots, len(available_examples))]
        return selected

    def _format_example_for_entity_prompt(self, example: Dict) -> str:
        """Format a training example for entity extraction prompt"""
        lines = [f"Will Text: {example['will_text']}"]

        # Add entity annotations
        for entity in example['entities']:
            lines.append("---")
            lines.append(f"Annotation Text: {entity['text']}")
            lines.append(f"Start Index: {entity['start']}")
            lines.append(f"End Index: {entity['end']}")
            lines.append(f"Annotation Labels: ['{entity['label']}']")

        return "\n".join(lines)

    def _format_example_for_relation_prompt(self, example: Dict) -> str:
        """Format a training example for relation extraction prompt"""
        lines = [f"Will Text: {example['will_text']}"]

        # Add entity information first
        lines.append("\nEntities:")
        for i, entity in enumerate(example['entities']):
            lines.append(f"Entity ID: {entity.get('entity_id', f'entity_{i}')}")
            lines.append(f"Annotation Text: {entity['text']}")
            lines.append(f"Start Index: {entity['start']}")
            lines.append(f"End Index: {entity['end']}")
            lines.append(f"Annotation Labels: ['{entity['label']}']")
            lines.append("---")

        # Add relation annotations
        lines.append("\nRelations:")
        for relation in example['relations']:
            lines.append("---")
            lines.append(f"Relation Labels: ['{relation['relation_type']}']")
            lines.append(f"from_id: {relation['from_id']}")
            lines.append(f"to_id: {relation['to_id']}")

        return "\n".join(lines)

    def extract_entities_from_text(self, will_text: str, target_domain: str = "in_domain") -> List[Entity]:
        """
        Extract entities from will text using GPT-4 with few-shot learning

        Args:
            will_text: The will text to analyze
            target_domain: "in_domain" or "out_domain" for example selection

        Returns:
            List of extracted entities
        """
        # Build few-shot prompt
        prompt_parts = [self.entity_instruction]

        # Add few-shot examples
        few_shot_examples = self._select_few_shot_examples(target_domain)
        if few_shot_examples:
            prompt_parts.append("\nHere are some examples:")
            for i, example in enumerate(few_shot_examples):
                prompt_parts.append(f"\nExample {i+1}:")
                prompt_parts.append(self._format_example_for_entity_prompt(example))

        # Add the target text
        prompt_parts.append(f"\nNow extract entities from the following will statement:")
        prompt_parts.append(f"Will Text: {will_text}")
        prompt_parts.append("\nPlease provide the entities in the same format as the examples above:")

        prompt = "\n".join(prompt_parts)
        response = self._call_gpt4(prompt)
        return self._parse_entity_response(response)

    def extract_relations_from_text(self, will_text: str, entities: List[Entity], target_domain: str = "in_domain") -> List[Relation]:
        """
        Extract relations from will text using GPT-4 with few-shot learning

        Args:
            will_text: The will text to analyze
            entities: Previously extracted entities
            target_domain: "in_domain" or "out_domain" for example selection

        Returns:
            List of extracted relations
        """
        # Build few-shot prompt
        prompt_parts = [self.relation_instruction]

        # Add few-shot examples
        few_shot_examples = self._select_few_shot_examples(target_domain)
        if few_shot_examples:
            prompt_parts.append("\nHere are some examples:")
            for i, example in enumerate(few_shot_examples):
                prompt_parts.append(f"\nExample {i+1}:")
                prompt_parts.append(self._format_example_for_relation_prompt(example))

        # Add the target text and entities
        prompt_parts.append(f"\nNow extract relations from the following will statement:")
        prompt_parts.append(f"Will Text: {will_text}")
        prompt_parts.append("\nEntities:")

        # Format current entities
        entity_text = self._format_entities_for_prompt(entities)
        prompt_parts.append(entity_text)

        prompt_parts.append("\nPlease provide the relations in the same format as the examples above:")

        prompt = "\n".join(prompt_parts)
        response = self._call_gpt4(prompt)
        return self._parse_relation_response(response)

    def _format_entities_for_prompt(self, entities: List[Entity]) -> str:
        """Format entities for inclusion in relation extraction prompt"""
        entity_lines = []
        for i, entity in enumerate(entities):
            entity_lines.append(f"Entity ID: entity_{i}")
            entity_lines.append(f"Annotation Text: {entity.text}")
            entity_lines.append(f"Start Index: {entity.start}")
            entity_lines.append(f"End Index: {entity.end}")
            entity_lines.append(f"Annotation Labels: ['{entity.label}']")
            entity_lines.append("---")
        return "\n".join(entity_lines)

    def _parse_entity_response(self, response: str) -> List[Entity]:
        """Parse GPT-4 response to extract entities"""
        entities = []
        lines = response.split('\n')

        current_entity = {}
        for line in lines:
            line = line.strip()
            if line.startswith("Annotation Text:"):
                current_entity['text'] = line.split(":", 1)[1].strip()
            elif line.startswith("Start Index:"):
                try:
                    current_entity['start'] = int(line.split(":", 1)[1].strip())
                except ValueError:
                    continue
            elif line.startswith("End Index:"):
                try:
                    current_entity['end'] = int(line.split(":", 1)[1].strip())
                except ValueError:
                    continue
            elif line.startswith("Annotation Labels:"):
                label_text = line.split(":", 1)[1].strip()
                # Extract label from ['LABEL'] format
                match = re.search(r"\['([^']+)'\]", label_text)
                if match:
                    current_entity['label'] = match.group(1)
            elif line == "---" and len(current_entity) == 4:
                entities.append(Entity(**current_entity))
                current_entity = {}

        return entities

    def _parse_relation_response(self, response: str) -> List[Relation]:
        """Parse GPT-4 response to extract relations"""
        relations = []
        lines = response.split('\n')

        current_relation = {}
        for line in lines:
            line = line.strip()
            if line.startswith("Relation Labels:"):
                label_text = line.split(":", 1)[1].strip()
                # Extract relation type from ['RELATION'] format
                match = re.search(r"\['([^']+)'\]", label_text)
                if match:
                    current_relation['relation_type'] = match.group(1)
            elif line.startswith("to_id:"):
                current_relation['to_entity'] = line.split(":", 1)[1].strip()
            elif line.startswith("from_id:"):
                current_relation['from_entity'] = line.split(":", 1)[1].strip()
            elif line == "---" and len(current_relation) == 3:
                relations.append(Relation(**current_relation))
                current_relation = {}

        return relations

    def load_evaluation_data(self, filepath: str) -> List[Dict]:
        """
        Load evaluation data from file

        Args:
            filepath: Path to evaluation data file

        Returns:
            List of evaluation examples
        """
        examples = []
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()

        # Split by will text sections
        sections = content.split('===================================')

        for section in sections:
            if not section.strip():
                continue

            example = self._parse_evaluation_section(section)
            if example:
                examples.append(example)

        return examples

    def _parse_evaluation_section(self, section: str) -> Optional[Dict]:
        """Parse a single evaluation section"""
        lines = section.strip().split('\n')
        if not lines:
            return None

        # Extract will text
        will_text_line = lines[0]
        if not will_text_line.startswith("Will Text:"):
            return None

        will_text = will_text_line[10:].strip()  # Remove "Will Text: "

        # Parse entities and relations
        entities = []
        relations = []

        current_entity = {}
        current_relation = {}

        for line in lines[1:]:
            line = line.strip()
            if line.startswith("Entity ID:"):
                if current_entity:
                    entities.append(current_entity)
                current_entity = {'entity_id': line.split(":", 1)[1].strip()}
            elif line.startswith("Annotation Text:") and current_entity:
                current_entity['text'] = line.split(":", 1)[1].strip()
            elif line.startswith("Start Index:") and current_entity:
                try:
                    current_entity['start'] = int(line.split(":", 1)[1].strip())
                except ValueError:
                    pass
            elif line.startswith("End Index:") and current_entity:
                try:
                    current_entity['end'] = int(line.split(":", 1)[1].strip())
                except ValueError:
                    pass
            elif line.startswith("Annotation Labels:") and current_entity:
                label_text = line.split(":", 1)[1].strip()
                match = re.search(r"\['([^']+)'\]", label_text)
                if match:
                    current_entity['label'] = match.group(1)
            elif line.startswith("Relation Labels:"):
                if current_relation:
                    relations.append(current_relation)
                label_text = line.split(":", 1)[1].strip()
                match = re.search(r"\['([^']+)'\]", label_text)
                if match:
                    current_relation = {'relation_type': match.group(1)}
            elif line.startswith("to_id:") and current_relation:
                current_relation['to_id'] = line.split(":", 1)[1].strip()
            elif line.startswith("from_id:") and current_relation:
                current_relation['from_id'] = line.split(":", 1)[1].strip()

        # Add last entity/relation if exists
        if current_entity:
            entities.append(current_entity)
        if current_relation:
            relations.append(current_relation)

        return {
            'will_text': will_text,
            'entities': entities,
            'relations': relations
        }

    def evaluate_entities(self, predicted_entities: List[Entity], gold_entities: List[Dict]) -> EvaluationResult:
        """
        Evaluate entity extraction performance

        Args:
            predicted_entities: Entities predicted by GPT-4
            gold_entities: Gold standard entities

        Returns:
            Evaluation metrics
        """
        # Convert gold entities to Entity objects for comparison
        gold_entity_objects = []
        for gold in gold_entities:
            if all(key in gold for key in ['text', 'start', 'end', 'label']):
                gold_entity_objects.append(Entity(
                    text=gold['text'],
                    start=gold['start'],
                    end=gold['end'],
                    label=gold['label'],
                    entity_id=gold.get('entity_id')
                ))

        # Calculate matches
        true_positives = 0
        false_positives = 0
        false_negatives = 0

        # Track which gold entities have been matched
        matched_gold = set()

        # Check each predicted entity
        for pred in predicted_entities:
            match_found = False
            for i, gold in enumerate(gold_entity_objects):
                if i in matched_gold:
                    continue

                # Exact match criteria: same span and label
                if (pred.start == gold.start and
                    pred.end == gold.end and
                    pred.label == gold.label):
                    true_positives += 1
                    matched_gold.add(i)
                    match_found = True
                    break

            if not match_found:
                false_positives += 1

        # Count unmatched gold entities as false negatives
        false_negatives = len(gold_entity_objects) - len(matched_gold)

        # Calculate metrics
        precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0.0
        recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0.0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0

        return EvaluationResult(
            precision=precision,
            recall=recall,
            f1=f1,
            true_positives=true_positives,
            false_positives=false_positives,
            false_negatives=false_negatives
        )

    def evaluate_relations(self, predicted_relations: List[Relation], gold_relations: List[Dict],
                          predicted_entities: List[Entity], gold_entities: List[Dict]) -> EvaluationResult:
        """
        Evaluate relation extraction performance

        Args:
            predicted_relations: Relations predicted by GPT-4
            gold_relations: Gold standard relations
            predicted_entities: Predicted entities (for ID mapping)
            gold_entities: Gold standard entities (for ID mapping)

        Returns:
            Evaluation metrics
        """
        # Create entity ID mappings
        pred_id_to_entity = {f"entity_{i}": ent for i, ent in enumerate(predicted_entities)}
        gold_id_to_entity = {ent.get('entity_id', f"gold_{i}"): ent for i, ent in enumerate(gold_entities)}

        # Convert gold relations to Relation objects
        gold_relation_objects = []
        for gold in gold_relations:
            if all(key in gold for key in ['relation_type', 'from_id', 'to_id']):
                gold_relation_objects.append(Relation(
                    from_entity=gold['from_id'],
                    to_entity=gold['to_id'],
                    relation_type=gold['relation_type'],
                    from_id=gold['from_id'],
                    to_id=gold['to_id']
                ))

        # Calculate matches
        true_positives = 0
        false_positives = 0
        false_negatives = 0

        # Track which gold relations have been matched
        matched_gold = set()

        # Check each predicted relation
        for pred in predicted_relations:
            match_found = False
            for i, gold in enumerate(gold_relation_objects):
                if i in matched_gold:
                    continue

                # Match criteria: same relation type and entity spans
                if pred.relation_type == gold.relation_type:
                    # Check if the entities match by span
                    pred_from_entity = pred_id_to_entity.get(pred.from_entity)
                    pred_to_entity = pred_id_to_entity.get(pred.to_entity)
                    gold_from_entity = gold_id_to_entity.get(gold.from_id)
                    gold_to_entity = gold_id_to_entity.get(gold.to_id)

                    if (pred_from_entity and pred_to_entity and
                        gold_from_entity and gold_to_entity):

                        from_match = (pred_from_entity.start == gold_from_entity['start'] and
                                    pred_from_entity.end == gold_from_entity['end'])
                        to_match = (pred_to_entity.start == gold_to_entity['start'] and
                                  pred_to_entity.end == gold_to_entity['end'])

                        if from_match and to_match:
                            true_positives += 1
                            matched_gold.add(i)
                            match_found = True
                            break

            if not match_found:
                false_positives += 1

        # Count unmatched gold relations as false negatives
        false_negatives = len(gold_relation_objects) - len(matched_gold)

        # Calculate metrics
        precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0.0
        recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0.0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0

        return EvaluationResult(
            precision=precision,
            recall=recall,
            f1=f1,
            true_positives=true_positives,
            false_positives=false_positives,
            false_negatives=false_negatives
        )
