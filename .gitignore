~$*

_site/
.sass-cache/
.jekyll-cache/
.jekyll-metadata

.DS_Store

.idea
target
project/target

# MacOS:
.DS_Store

# JetBrains (PyCharm)
.idea

# VS Code
.vscode

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest*

# Generated files from program analysis
*_lambdas.py
preprocessed*
!tests/data/**/*_lambdas.py

# Ruby stuff
Gemfile.lock

# Scala stuff
*.metals

# NodeJS webservice stuff
*node_modules
*package-lock.json

# Adding lib files for TR webapp
!src/text_reading/webapp/public/brat/client/lib

# Jupyter notebook checkpoints
*-checkpoint.ipynb

# Program analysis stuff
*modFileLog.json
*--GrFN.pdf
tmp

# Model assembly stuff
*align_payload.json

# Docker build stuff
.env
