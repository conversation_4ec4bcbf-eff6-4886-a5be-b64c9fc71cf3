# GPT-4 Legal Wills Information Extraction Evaluation Guide

本指南详细说明如何使用我们实现的GPT-4评估框架来复现论文"Information Extraction from Legal Wills: How Well Does GPT-4 Do?"的实验结果。

## 📋 项目复现状态

### ✅ 已完成的工作

1. **环境配置** ✅
   - 创建conda虚拟环境 `ie4wills`
   - 安装所有必需依赖：`pdf2image`, `pytesseract`, `openai`, `pandas`
   - 验证Tesseract OCR引擎可用

2. **数据准备** ✅
   - 验证原始数据集完整性
   - 改进数据转换脚本，支持UTF-8编码
   - 生成所有评估数据集：train, dev, test, ood

3. **评估框架实现** ✅
   - 完整的GPT-4 API调用模块
   - 实体抽取评估功能
   - 关系抽取评估功能
   - 评估指标计算（Precision, Recall, F1）
   - 结果保存和可视化

4. **测试验证** ✅
   - 所有核心功能测试通过
   - 数据加载和解析正常
   - 评估指标计算正确

## 🚀 快速开始

### 1. 环境准备

确保您已经激活了conda虚拟环境：
```bash
conda activate ie4wills
```

### 2. 获取OpenAI API密钥

要运行GPT-4评估，您需要：
1. 在 [OpenAI官网](https://platform.openai.com/) 注册账户
2. 获取API密钥
3. 确保账户有足够的余额（建议至少$10-20用于完整评估）

### 3. 运行测试

首先运行测试脚本验证框架正常工作：
```bash
python src/test_evaluator.py
```

### 4. 小规模测试评估

使用少量样本测试GPT-4评估（需要API密钥）：
```bash
python src/run_evaluation.py --api-key YOUR_API_KEY --max-examples 3
```

### 5. 完整评估

运行完整的评估实验：
```bash
python src/run_evaluation.py --api-key YOUR_API_KEY
```

## 📊 评估设置

### 数据集
- **train**: 训练集数据（用于in-context learning示例）
- **dev**: 开发集数据
- **test**: 测试集数据
- **ood**: 域外数据（out-of-domain，来自不同州的遗嘱）

### 评估任务
1. **实体抽取**: 识别TESTATOR、BENEFICIARY、ASSET、WILL四类实体
2. **关系抽取**: 识别TESTATOR-BENEFICIARY、TESTATOR-ASSET、BENEFICIARY-ASSET、TESTATOR-WILL四类关系

### 评估指标
- **Precision**: 精确率 = TP / (TP + FP)
- **Recall**: 召回率 = TP / (TP + FN)
- **F1 Score**: F1分数 = 2 × (Precision × Recall) / (Precision + Recall)

## 🔧 高级配置

### 命令行参数

```bash
python src/run_evaluation.py [OPTIONS]

选项:
  --api-key TEXT          OpenAI API密钥 [必需]
  --model TEXT           GPT模型名称 (默认: gpt-4)
  --data-dir TEXT        评估数据目录 (默认: data/evaluation/evaluation)
  --output-dir TEXT      结果输出目录 (默认: results)
  --datasets TEXT        要评估的数据集文件列表
  --max-examples INTEGER 每个数据集的最大样本数（用于测试）
  --save-predictions     保存详细预测结果到JSON文件
```

### 示例用法

1. **测试单个数据集**：
```bash
python src/run_evaluation.py --api-key YOUR_API_KEY --datasets dev-evaluation.txt --max-examples 5
```

2. **使用不同的GPT模型**：
```bash
python src/run_evaluation.py --api-key YOUR_API_KEY --model gpt-4-turbo --max-examples 10
```

3. **保存详细预测结果**：
```bash
python src/run_evaluation.py --api-key YOUR_API_KEY --save-predictions --max-examples 5
```

## 📈 结果分析

### 输出文件
- `results/evaluation_results_TIMESTAMP.csv`: 主要评估指标
- `results/predictions_TIMESTAMP.json`: 详细预测结果（如果启用）

### 预期结果
根据论文，GPT-4在该任务上的表现：
- **实体抽取**: F1分数约在0.6-0.8之间
- **关系抽取**: F1分数约在0.4-0.6之间
- **域外数据**: 性能通常比域内数据略低

## ⚠️ 注意事项

### API成本估算
- 每个遗嘱样本大约消耗500-1000 tokens
- 完整评估（所有数据集）预计消耗约50,000-100,000 tokens
- 按GPT-4定价（$0.03/1K tokens），完整评估成本约$1.5-3

### 性能优化
1. **使用max-examples参数**进行小规模测试
2. **分批运行**不同数据集以避免长时间运行
3. **监控API配额**避免超出限制

### 错误处理
- 脚本包含重试机制处理API临时错误
- 如果遇到持续错误，检查API密钥和网络连接
- 可以从中断的地方继续运行评估

## 🔍 故障排除

### 常见问题

1. **API密钥错误**
   ```
   Error: Please provide a valid OpenAI API key
   ```
   解决：检查API密钥是否正确，是否有足够余额

2. **编码错误**
   ```
   UnicodeEncodeError: 'gbk' codec can't encode character
   ```
   解决：已在代码中修复，使用UTF-8编码

3. **数据文件未找到**
   ```
   Warning: Dataset file not found
   ```
   解决：检查数据文件路径，确保运行了数据预处理步骤

4. **内存不足**
   解决：使用`--max-examples`参数限制样本数量

## 📚 代码结构

```
src/
├── gpt4_evaluator.py      # 核心评估类
├── run_evaluation.py      # 主评估脚本
├── test_evaluator.py      # 测试脚本
├── raw_to_evaluation_datasets.py  # 数据转换脚本
└── pdf_to_text.py         # OCR处理脚本

data/
├── raw/                   # 原始数据
├── evaluation/
│   ├── train-dev-test-split/  # 数据集分割
│   └── evaluation/        # 评估格式数据

prompt/
├── evaluation_prompt_for_entity.txt    # 实体抽取提示
└── evaluation_prompt_for_relation.txt  # 关系抽取提示

results/                   # 评估结果输出目录
```

## 🎯 下一步

1. **运行完整评估**：获得所有数据集的完整结果
2. **结果分析**：与论文报告的结果进行对比
3. **错误分析**：分析GPT-4的典型错误模式
4. **改进实验**：尝试不同的提示策略或模型版本

## 📞 支持

如果遇到问题，请：
1. 首先运行测试脚本确认环境配置
2. 检查API密钥和网络连接
3. 查看错误日志获取详细信息
4. 参考本指南的故障排除部分
