#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Main script to run GPT-4 evaluation on legal wills IE task

This script runs the complete evaluation pipeline:
1. Load evaluation data
2. Run GPT-4 entity extraction
3. Run GPT-4 relation extraction
4. Calculate evaluation metrics
5. Save results
"""

import argparse
import json
import os
import sys
from datetime import datetime
from typing import Dict, List
import pandas as pd

from gpt4_evaluator import GPT4Evaluator, EvaluationResult

def print_results(results: Dict[str, EvaluationResult], task_name: str):
    """Print evaluation results in a formatted way"""
    print(f"\n{'='*50}")
    print(f"{task_name} EVALUATION RESULTS")
    print(f"{'='*50}")

    for dataset_name, result in results.items():
        print(f"\n{dataset_name.upper()} Dataset:")
        print(f"  Precision: {result.precision:.4f}")
        print(f"  Recall:    {result.recall:.4f}")
        print(f"  F1 Score:  {result.f1:.4f}")
        print(f"  True Positives:  {result.true_positives}")
        print(f"  False Positives: {result.false_positives}")
        print(f"  False Negatives: {result.false_negatives}")

def save_results_to_csv(entity_results: Dict[str, EvaluationResult],
                       relation_results: Dict[str, EvaluationResult],
                       output_dir: str):
    """Save results to CSV files"""

    # Prepare data for CSV
    data = []

    # Entity results
    for dataset, result in entity_results.items():
        data.append({
            'Task': 'Entity Extraction',
            'Dataset': dataset,
            'Precision': result.precision,
            'Recall': result.recall,
            'F1': result.f1,
            'True_Positives': result.true_positives,
            'False_Positives': result.false_positives,
            'False_Negatives': result.false_negatives
        })

    # Relation results
    for dataset, result in relation_results.items():
        data.append({
            'Task': 'Relation Extraction',
            'Dataset': dataset,
            'Precision': result.precision,
            'Recall': result.recall,
            'F1': result.f1,
            'True_Positives': result.true_positives,
            'False_Positives': result.false_positives,
            'False_Negatives': result.false_negatives
        })

    # Create DataFrame and save
    df = pd.DataFrame(data)

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Save to CSV
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    csv_path = os.path.join(output_dir, f"evaluation_results_{timestamp}.csv")
    df.to_csv(csv_path, index=False)
    print(f"\nResults saved to: {csv_path}")

    return csv_path

def run_evaluation_on_dataset(evaluator: GPT4Evaluator, dataset_path: str,
                             dataset_name: str, max_examples: int = None,
                             train_examples: List[Dict] = None) -> tuple:
    """
    Run evaluation on a single dataset

    Args:
        evaluator: GPT4Evaluator instance
        dataset_path: Path to evaluation dataset
        dataset_name: Name of the dataset
        max_examples: Maximum number of examples to evaluate (for testing)
        train_examples: Training examples for in-context learning

    Returns:
        Tuple of (entity_result, relation_result)
    """
    print(f"\nEvaluating {dataset_name} dataset...")
    print(f"Loading data from: {dataset_path}")

    # Load evaluation data
    examples = evaluator.load_evaluation_data(dataset_path)

    if max_examples:
        examples = examples[:max_examples]
        print(f"Limiting to {max_examples} examples for testing")

    print(f"Loaded {len(examples)} examples")

    # Track predictions and gold standards
    all_predicted_entities = []
    all_gold_entities = []
    all_predicted_relations = []
    all_gold_relations = []

    # Determine domain for few-shot example selection
    target_domain = "out_domain" if dataset_name == "ood" else "in_domain"

    # Process each example
    for i, example in enumerate(examples):
        print(f"Processing example {i+1}/{len(examples)}...", end=' ')

        try:
            # Extract entities using GPT-4 with few-shot learning
            predicted_entities = evaluator.extract_entities_from_text(
                example['will_text'], target_domain=target_domain
            )

            # Extract relations using GPT-4 with few-shot learning
            predicted_relations = evaluator.extract_relations_from_text(
                example['will_text'], predicted_entities, target_domain=target_domain
            )

            # Collect predictions and gold standards
            all_predicted_entities.extend(predicted_entities)
            all_gold_entities.extend(example['entities'])
            all_predicted_relations.extend(predicted_relations)
            all_gold_relations.extend(example['relations'])

            print("✓")

        except Exception as e:
            print(f"✗ Error: {e}")
            continue

    # Calculate evaluation metrics
    print("Calculating entity extraction metrics...")
    entity_result = evaluator.evaluate_entities(all_predicted_entities, all_gold_entities)

    print("Calculating relation extraction metrics...")
    relation_result = evaluator.evaluate_relations(
        all_predicted_relations, all_gold_relations,
        all_predicted_entities, all_gold_entities
    )

    return entity_result, relation_result

def main():
    parser = argparse.ArgumentParser(description='Run GPT-4 evaluation on legal wills IE task')
    parser.add_argument('--api-key', default='sk-1PYK9cXL2KoDuuOKZ3PzeHf9HRxXmsrF6tLnetB4dej8bwoJ',
                       help='API key (default: pre-configured)')
    parser.add_argument('--model', default='gpt-4', help='GPT model to use (default: gpt-4)')
    parser.add_argument('--data-dir', default='data/evaluation/evaluation',
                       help='Directory containing evaluation data files')
    parser.add_argument('--output-dir', default='results',
                       help='Directory to save results')
    parser.add_argument('--datasets', nargs='+',
                       default=['dev-evaluation.txt', 'test-evaluation.txt', 'ood-evaluation.txt'],
                       help='Dataset files to evaluate (train is used for few-shot examples)')
    parser.add_argument('--max-examples', type=int, default=None,
                       help='Maximum number of examples per dataset (for testing)')
    parser.add_argument('--num-shots', type=int, default=3,
                       help='Number of few-shot examples to use (default: 3)')
    parser.add_argument('--save-predictions', action='store_true',
                       help='Save detailed predictions to JSON files')

    args = parser.parse_args()

    # Validate API key
    if not args.api_key or args.api_key == 'your_api_key_here':
        print("Error: Please provide a valid API key")
        sys.exit(1)

    # Load training data for few-shot learning
    print("Loading training data for few-shot learning...")
    train_data_path = os.path.join(args.data_dir, 'train-evaluation.txt')
    train_examples = []

    if os.path.exists(train_data_path):
        temp_evaluator = GPT4Evaluator(api_key="temp")  # Temporary evaluator for data loading
        train_examples = temp_evaluator.load_evaluation_data(train_data_path)
        print(f"Loaded {len(train_examples)} training examples for few-shot learning")
    else:
        print(f"Warning: Training data file {train_data_path} not found. Running without few-shot examples.")

    # Initialize evaluator with training examples
    print("Initializing GPT-4 evaluator...")
    evaluator = GPT4Evaluator(
        api_key=args.api_key,
        model=args.model,
        train_examples=train_examples,
        num_shots=args.num_shots
    )

    # Run evaluation on each dataset
    entity_results = {}
    relation_results = {}

    for dataset_file in args.datasets:
        dataset_path = os.path.join(args.data_dir, dataset_file)

        if not os.path.exists(dataset_path):
            print(f"Warning: Dataset file {dataset_path} not found, skipping...")
            continue

        dataset_name = dataset_file.replace('-evaluation.txt', '').replace('.txt', '')

        try:
            entity_result, relation_result = run_evaluation_on_dataset(
                evaluator, dataset_path, dataset_name, args.max_examples, train_examples
            )

            entity_results[dataset_name] = entity_result
            relation_results[dataset_name] = relation_result

        except Exception as e:
            print(f"Error evaluating {dataset_name}: {e}")
            continue

    # Print results
    print_results(entity_results, "ENTITY EXTRACTION")
    print_results(relation_results, "RELATION EXTRACTION")

    # Save results
    if entity_results or relation_results:
        try:
            csv_path = save_results_to_csv(entity_results, relation_results, args.output_dir)
            print(f"\nEvaluation completed successfully!")
            print(f"Results saved to: {csv_path}")
        except Exception as e:
            print(f"Error saving results: {e}")
    else:
        print("\nNo results to save.")

if __name__ == "__main__":
    main()
