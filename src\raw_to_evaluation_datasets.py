# -*- coding: utf-8 -*-
"""raw_to_evaluation_datasets.py

Script to convert raw dataset to evaluation format.
Modified to be more flexible and accept command line arguments.
"""

import json
import sys
import argparse
import os

class Logger(object):
    def __init__(self, filename="test-annotations.txt"):
        self.filename = filename
        self.terminal = sys.stdout
        self.log = None

    def open(self):
        self.log = open(self.filename, "w", encoding='utf-8')  # Added UTF-8 encoding

    def close(self):
        if self.log:
            self.log.close()

    def write(self, message):
        self.terminal.write(message)
        if self.log:
            self.log.write(message)

    def flush(self):
        pass

def main():
    parser = argparse.ArgumentParser(description='Convert raw dataset to evaluation format')
    parser.add_argument('--input', '-i', required=True, help='Input JSON file path')
    parser.add_argument('--output', '-o', required=True, help='Output text file path')

    args = parser.parse_args()

    # Check if input file exists
    if not os.path.exists(args.input):
        print(f"Error: Input file {args.input} does not exist")
        return

    # Create an instance of the Logger class
    logger = Logger(args.output)
    logger.open()

    sys.stdout = logger

    # Open the JSON file
    with open(args.input, 'r', encoding='utf-8') as json_file:
        data = json.load(json_file)

    # Iterate over each item in the JSON data
    for item in data:
        # Extract the will text
        will_text = item['data']['text']
        sys.stdout.write("Will Text: {}\n".format(will_text))
        sys.stdout.write("---\n")

        # Extract and print the annotations
        annotations = item['annotations']
        for annotation in annotations:
            annotation_id = annotation['id']
            sys.stdout.write("Annotation ID: {}\n".format(annotation_id))
            sys.stdout.write("---\n")

            if 'result' in annotation and annotation['result']:
                results = annotation['result']
                for result in results:
                    if 'value' in result: # add the entities you wish to evaluate below (e.g., EXECUTOR, WITNESS, etc.)
                        if result['value']['labels'] == ['TESTATOR'] or result['value']['labels'] == ['BENEFICIARY'] or result['value']['labels'] == ['WILL'] or result['value']['labels'] == ['ASSET']:
                            id = result['id']
                            value = result['value']
                            annotation_text = value['text']
                            start_id = value['start']
                            end_id = value['end']
                            annotation_labels = value['labels']
                            sys.stdout.write("Entity ID: {}\n".format(id))
                            sys.stdout.write("Annotation Text: {}\n".format(annotation_text))
                            sys.stdout.write("Start Index: {}\n".format(start_id))
                            sys.stdout.write("End Index: {}\n".format(end_id))
                            sys.stdout.write("Annotation Labels: {}\n".format(annotation_labels))
                            sys.stdout.write("---\n")
                    if result['type'] == "relation": # add the relations you wish to evaluate below (e.g., 'TESTATOR-EXECUTOR', 'TESTATOR-WITNESS', etc.)
                        if result['labels'] == ['BENEFICIARY-ASSET'] or result['labels'] == ['TESTATOR-ASSET'] or result['labels'] == ['TESTATOR-BENEFICIARY'] or result['labels'] == ['TESTATOR-WILL']:
                            to_id = result['to_id']
                            from_id = result['from_id']
                            relation_labels = result['labels']
                            sys.stdout.write("Relation Labels: {}\n".format(relation_labels))
                            sys.stdout.write("to_id: {}\n".format(to_id))
                            sys.stdout.write("from_id: {}\n".format(from_id))
                            sys.stdout.write("---\n")
        sys.stdout.write("===================================\n")

    logger.close()
    # Reset stdout to terminal
    sys.stdout = logger.terminal
    print(f"Conversion completed. Output saved to {args.output}")

if __name__ == "__main__":
    main()