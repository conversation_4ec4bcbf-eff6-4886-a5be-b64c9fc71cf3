#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for custom API endpoint

This script tests the custom API endpoint with your specific API key
to ensure it works correctly before running the full evaluation.
"""

import requests
import json
from gpt4_evaluator import GPT4Evaluator

# 配置API信息
API_KEY = "sk-1PYK9cXL2KoDuuOKZ3PzeHf9HRxXmsrF6tLnetB4dej8bwoJ"
API_URL = "https://chatapi.littlewheat.com/v1/chat/completions"

def test_direct_api_call():
    """Test direct API call using requests"""
    print("Testing direct API call...")

    url = API_URL
    api_key = API_KEY

    payload = {
        "model": "gpt-4",
        "messages": [
            {
                "role": "user",
                "content": "你好! 请简单回复一句话确认API正常工作。"
            }
        ],
        "stream": False,
        "temperature": 0.0,
        "max_tokens": 100
    }

    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }

    try:
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        response.raise_for_status()

        response_data = response.json()
        print(f"✓ Direct API call successful!")
        print(f"Response: {response_data['choices'][0]['message']['content']}")
        return True

    except Exception as e:
        print(f"✗ Direct API call failed: {e}")
        if hasattr(e, 'response') and e.response is not None:
            print(f"Response status: {e.response.status_code}")
            print(f"Response text: {e.response.text}")
        return False

def test_evaluator_api_call():
    """Test API call through our evaluator class"""
    print("\nTesting API call through GPT4Evaluator...")

    api_key = API_KEY

    try:
        # Initialize evaluator with custom API
        evaluator = GPT4Evaluator(api_key=api_key)

        # Test a simple prompt
        test_prompt = "Please extract entities from this text: 'I, John Smith, give my house to my son.'"

        response = evaluator._call_gpt4(test_prompt)
        print(f"✓ Evaluator API call successful!")
        print(f"Response preview: {response[:200]}...")
        return True

    except Exception as e:
        print(f"✗ Evaluator API call failed: {e}")
        return False

def test_entity_extraction():
    """Test entity extraction with a simple will text"""
    print("\nTesting entity extraction...")

    api_key = API_KEY

    try:
        evaluator = GPT4Evaluator(api_key=api_key)

        # Simple test will text
        test_will = "I, John Smith, hereby give my house located at 123 Main Street to my beloved son, Michael Smith. This is my last will and testament."

        print(f"Test will text: {test_will}")

        # Extract entities
        entities = evaluator.extract_entities_from_text(test_will)

        print(f"✓ Entity extraction successful!")
        print(f"Found {len(entities)} entities:")

        for i, entity in enumerate(entities):
            print(f"  {i+1}. '{entity.text}' ({entity.label}) [{entity.start}:{entity.end}]")

        return True

    except Exception as e:
        print(f"✗ Entity extraction failed: {e}")
        return False

def main():
    """Run all tests"""
    print("="*60)
    print("TESTING CUSTOM API ENDPOINT")
    print("="*60)
    print(f"API Key: {API_KEY}")
    print(f"API URL: {API_URL}")
    print("="*60)

    # Run tests
    test1_passed = test_direct_api_call()
    test2_passed = test_evaluator_api_call()
    test3_passed = test_entity_extraction()

    print("\n" + "="*60)
    print("TEST RESULTS SUMMARY")
    print("="*60)
    print(f"Direct API call:      {'✓ PASSED' if test1_passed else '✗ FAILED'}")
    print(f"Evaluator API call:   {'✓ PASSED' if test2_passed else '✗ FAILED'}")
    print(f"Entity extraction:    {'✓ PASSED' if test3_passed else '✗ FAILED'}")

    if all([test1_passed, test2_passed, test3_passed]):
        print("\n🎉 All tests passed! Your API is ready for evaluation.")
        print("\nYou can now run the full evaluation with:")
        print("python src/run_evaluation.py --max-examples 3")
    else:
        print("\n❌ Some tests failed. Please check your API key and network connection.")
        print("\nTroubleshooting tips:")
        print("1. Verify your API key is correct")
        print("2. Check if you have sufficient balance")
        print("3. Ensure network connectivity to the API endpoint")
        print("4. Try the direct API call test first")

if __name__ == "__main__":
    main()
